# OSX
.DS_Store

# Xcode
build/
*.pbxuser
!default.pbxuser
*.mode1v3
!default.mode1v3
*.mode2v3
!default.mode2v3
*.perspectivev3
!default.perspectivev3
xcuserdata
*.xccheckout
*.moved-aside
DerivedData
*.hmap
*.ipa
*.xcuserstate
ios/.xcode.env.local

# Android/IntelliJ
build/
.idea
.gradle
local.properties
*.iml

# node.js
node_modules/
npm-debug.log
yarn-debug.log
yarn-error.log

# fastlane
**/fastlane/report.xml
**/fastlane/Preview.html
**/fastlane/screenshots
**/fastlane/test_output

# Bundle artifact
*.jsbundle

# Metro
.metro-health-check*

# Babel
.env

# VS Code
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json

# Env files
.env.local
.env.development.local
.env.test.local
.env.production.local

# temp
.temp/

# Logs
logs
*.log

# CocoaPods
ios/Pods/

# Android build files
android/app/build/
android/build/
*.keystore

# Bundle
guzo-driver-app-*.json

# Expo
.expo/
dist/
dist-ssr/
*.jks
*.p8
*.p12
*.key
*.mobileprovision

# Typescript
*.tsbuildinfo

# Misc
*.orig
*.log.*
*.swp
.DS_Store
.env

# Temporary files created by Metro for checking the health of the file watcher
.metro-health-check*
