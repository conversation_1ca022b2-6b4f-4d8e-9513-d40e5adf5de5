{"compilerOptions": {"target": "esnext", "module": "commonjs", "lib": ["es2017"], "allowJs": true, "jsx": "react-native", "noEmit": true, "isolatedModules": true, "strict": true, "moduleResolution": "node", "allowSyntheticDefaultImports": true, "esModuleInterop": true, "skipLibCheck": true, "resolveJsonModule": true, "baseUrl": ".", "paths": {"@/*": ["src/*"], "@components/*": ["src/components/*"], "@screens/*": ["src/screens/*"], "@navigation/*": ["src/navigation/*"], "@services/*": ["src/services/*"], "@store/*": ["src/store/*"], "@assets/*": ["src/assets/*"], "@utils/*": ["src/utils/*"], "@theme": ["src/theme"], "@types": ["src/types"]}}, "exclude": ["node_modules", "babel.config.js", "metro.config.js", "jest.config.js"], "extends": "@react-native/typescript-config/tsconfig.json"}