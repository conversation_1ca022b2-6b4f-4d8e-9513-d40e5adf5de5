import React from 'react';
import {createStackNavigator} from '@react-navigation/stack';

import LoginScreen from '@screens/auth/LoginScreen';
import {AuthStackParamList} from '@types/index';

const Stack = createStackNavigator<AuthStackParamList>();

const AuthNavigator: React.FC = () => {
  return (
    <Stack.Navigator
      screenOptions={{
        headerShown: false,
      }}>
      <Stack.Screen name="Login" component={LoginScreen} />
    </Stack.Navigator>
  );
};

export default AuthNavigator;
