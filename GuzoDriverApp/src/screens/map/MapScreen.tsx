import React, {useEffect, useRef, useState} from 'react';
import {View, StyleSheet, Alert} from 'react-native';
import {Text, Card, FAB, Chip} from 'react-native-paper';
import {useDispatch, useSelector} from 'react-redux';
import MapboxGL from '@rnmapbox/maps';
import Icon from 'react-native-vector-icons/MaterialIcons';

import {RootState, AppDispatch} from '@store/index';
import {startLocationTracking, getCurrentLocation} from '@store/slices/locationSlice';
import {getDriverSchedules} from '@store/slices/routeSlice';
import {colors, spacing, typography, shadows} from '@theme/index';

// Set your Mapbox access token here
MapboxGL.setAccessToken('YOUR_MAPBOX_ACCESS_TOKEN'); // Replace with actual token

const MapScreen: React.FC = () => {
  const dispatch = useDispatch<AppDispatch>();
  const {currentLocation, isTracking} = useSelector(
    (state: RootState) => state.location,
  );
  const {currentSchedule} = useSelector((state: RootState) => state.route);

  const mapRef = useRef<MapboxGL.MapView>(null);
  const cameraRef = useRef<MapboxGL.Camera>(null);
  const [followUser, setFollowUser] = useState(true);

  useEffect(() => {
    // Get current location and start tracking
    dispatch(getCurrentLocation());
    if (!isTracking) {
      dispatch(startLocationTracking());
    }
  }, [dispatch, isTracking]);

  useEffect(() => {
    // Center map on user location when it's available
    if (currentLocation && followUser && cameraRef.current) {
      cameraRef.current.setCamera({
        centerCoordinate: [currentLocation.longitude, currentLocation.latitude],
        zoomLevel: 15,
        animationDuration: 1000,
      });
    }
  }, [currentLocation, followUser]);

  const handleCenterOnUser = () => {
    if (currentLocation && cameraRef.current) {
      setFollowUser(true);
      cameraRef.current.setCamera({
        centerCoordinate: [currentLocation.longitude, currentLocation.latitude],
        zoomLevel: 15,
        animationDuration: 1000,
      });
    } else {
      Alert.alert('Location not available', 'Please enable location tracking');
    }
  };

  const handleMapPress = () => {
    setFollowUser(false);
  };

  // Create route line coordinates
  const routeCoordinates = currentSchedule?.route?.stops
    ? currentSchedule.route.stops
        .sort((a, b) => a.order - b.order)
        .map(stop => [stop.longitude, stop.latitude])
    : [];

  return (
    <View style={styles.container}>
      {/* Route Info Card */}
      {currentSchedule && (
        <Card style={styles.routeCard}>
          <Card.Content style={styles.routeContent}>
            <View style={styles.routeHeader}>
              <Text style={styles.routeTitle}>{currentSchedule.route.name}</Text>
              <Chip
                style={{backgroundColor: colors.primary}}
                textStyle={{color: colors.textLight}}>
                {currentSchedule.route.stops?.length || 0} stops
              </Chip>
            </View>
            <Text style={styles.routeTime}>
              {currentSchedule.startTime} - {currentSchedule.endTime}
            </Text>
          </Card.Content>
        </Card>
      )}

      {/* Map */}
      <View style={styles.mapContainer}>
        <MapboxGL.MapView
          ref={mapRef}
          style={styles.map}
          onPress={handleMapPress}
          compassEnabled={true}
          compassViewPosition={3} // Top right
          logoEnabled={false}
          attributionEnabled={false}>
          
          <MapboxGL.Camera
            ref={cameraRef}
            zoomLevel={12}
            centerCoordinate={
              currentLocation
                ? [currentLocation.longitude, currentLocation.latitude]
                : [38.7469, 9.0579] // Default to Addis Ababa
            }
            followUserLocation={followUser}
            followUserMode="normal"
          />

          {/* User location marker */}
          {currentLocation && (
            <MapboxGL.PointAnnotation
              id="userLocation"
              coordinate={[currentLocation.longitude, currentLocation.latitude]}>
              <View style={styles.userMarker}>
                <Icon name="my-location" size={20} color={colors.textLight} />
              </View>
            </MapboxGL.PointAnnotation>
          )}

          {/* Route line */}
          {routeCoordinates.length > 1 && (
            <MapboxGL.ShapeSource
              id="routeSource"
              shape={{
                type: 'Feature',
                properties: {},
                geometry: {
                  type: 'LineString',
                  coordinates: routeCoordinates,
                },
              }}>
              <MapboxGL.LineLayer
                id="routeLine"
                style={{
                  lineColor: colors.primary,
                  lineWidth: 4,
                  lineOpacity: 0.8,
                }}
              />
            </MapboxGL.ShapeSource>
          )}

          {/* Bus stop markers */}
          {currentSchedule?.route?.stops?.map((stop, index) => (
            <MapboxGL.PointAnnotation
              key={stop.id}
              id={`stop-${stop.id}`}
              coordinate={[stop.longitude, stop.latitude]}>
              <View style={styles.stopMarker}>
                <Text style={styles.stopNumber}>{stop.order}</Text>
              </View>
              <MapboxGL.Callout title={stop.name} />
            </MapboxGL.PointAnnotation>
          ))}
        </MapboxGL.MapView>

        {/* Location tracking status */}
        <View style={styles.statusContainer}>
          <Chip
            style={{
              backgroundColor: isTracking ? colors.success : colors.error,
            }}
            textStyle={{color: colors.textLight}}
            icon={isTracking ? 'gps-fixed' : 'gps-off'}>
            {isTracking ? 'Tracking Active' : 'Tracking Inactive'}
          </Chip>
        </View>

        {/* Center on user FAB */}
        <FAB
          style={[
            styles.centerFab,
            {backgroundColor: followUser ? colors.primary : colors.textSecondary},
          ]}
          icon="my-location"
          onPress={handleCenterOnUser}
          size="medium"
        />
      </View>

      {/* Location info */}
      {currentLocation && (
        <Card style={styles.locationCard}>
          <Card.Content style={styles.locationContent}>
            <View style={styles.locationRow}>
              <Icon name="location-on" size={16} color={colors.textSecondary} />
              <Text style={styles.locationText}>
                {currentLocation.latitude.toFixed(6)}, {currentLocation.longitude.toFixed(6)}
              </Text>
            </View>
            <View style={styles.locationRow}>
              <Icon name="speed" size={16} color={colors.textSecondary} />
              <Text style={styles.locationText}>
                Accuracy: ±{Math.round(currentLocation.accuracy || 0)}m
              </Text>
            </View>
          </Card.Content>
        </Card>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  routeCard: {
    margin: spacing.md,
    marginBottom: spacing.sm,
    ...shadows.small,
  },
  routeContent: {
    paddingVertical: spacing.sm,
  },
  routeHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.xs,
  },
  routeTitle: {
    ...typography.h3,
    color: colors.text,
    flex: 1,
  },
  routeTime: {
    ...typography.body2,
    color: colors.textSecondary,
  },
  mapContainer: {
    flex: 1,
    position: 'relative',
  },
  map: {
    flex: 1,
  },
  userMarker: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 3,
    borderColor: colors.textLight,
  },
  stopMarker: {
    width: 30,
    height: 30,
    borderRadius: 15,
    backgroundColor: colors.secondary,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: colors.textLight,
  },
  stopNumber: {
    color: colors.textLight,
    fontSize: 12,
    fontWeight: 'bold',
  },
  statusContainer: {
    position: 'absolute',
    top: spacing.md,
    left: spacing.md,
  },
  centerFab: {
    position: 'absolute',
    bottom: spacing.xl,
    right: spacing.md,
  },
  locationCard: {
    margin: spacing.md,
    marginTop: spacing.sm,
    ...shadows.small,
  },
  locationContent: {
    paddingVertical: spacing.sm,
  },
  locationRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.xs,
  },
  locationText: {
    ...typography.caption,
    color: colors.textSecondary,
    marginLeft: spacing.xs,
  },
});

export default MapScreen;
