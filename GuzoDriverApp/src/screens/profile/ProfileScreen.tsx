import React from 'react';
import {View, StyleSheet, ScrollView, Alert} from 'react-native';
import {Text, Card, Avatar, Divider} from 'react-native-paper';
import {useDispatch, useSelector} from 'react-redux';
import Icon from 'react-native-vector-icons/MaterialIcons';

import {RootState, AppDispatch} from '@store/index';
import {logoutUser} from '@store/slices/authSlice';
import {stopLocationTracking} from '@store/slices/locationSlice';
import Button from '@components/ui/Button';
import {colors, spacing, typography, shadows} from '@theme/index';

const ProfileScreen: React.FC = () => {
  const dispatch = useDispatch<AppDispatch>();
  const {user, isLoading} = useSelector((state: RootState) => state.auth);
  const {isTracking} = useSelector((state: RootState) => state.location);

  const handleLogout = () => {
    Alert.alert(
      'Logout',
      'Are you sure you want to logout?',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Logout',
          style: 'destructive',
          onPress: async () => {
            // Stop location tracking before logout
            if (isTracking) {
              await dispatch(stopLocationTracking());
            }
            dispatch(logoutUser());
          },
        },
      ],
    );
  };

  const getInitials = (firstName?: string, lastName?: string) => {
    const first = firstName?.charAt(0)?.toUpperCase() || '';
    const last = lastName?.charAt(0)?.toUpperCase() || '';
    return `${first}${last}` || 'DR';
  };

  const profileItems = [
    {
      icon: 'person',
      label: 'Full Name',
      value: `${user?.firstName || ''} ${user?.lastName || ''}`.trim() || 'Not provided',
    },
    {
      icon: 'alternate-email',
      label: 'Username',
      value: user?.username || 'Not provided',
    },
    {
      icon: 'email',
      label: 'Email',
      value: user?.email || 'Not provided',
    },
    {
      icon: 'phone',
      label: 'Phone',
      value: user?.phone || 'Not provided',
    },
    {
      icon: 'work',
      label: 'Role',
      value: user?.role || 'Driver',
    },
    {
      icon: 'verified',
      label: 'Status',
      value: user?.isActive ? 'Active' : 'Inactive',
    },
  ];

  return (
    <ScrollView style={styles.container}>
      {/* Profile Header */}
      <Card style={styles.headerCard}>
        <Card.Content style={styles.headerContent}>
          <Avatar.Text
            size={80}
            label={getInitials(user?.firstName, user?.lastName)}
            style={styles.avatar}
          />
          <View style={styles.headerInfo}>
            <Text style={styles.name}>
              {`${user?.firstName || ''} ${user?.lastName || ''}`.trim() || 'Driver'}
            </Text>
            <Text style={styles.role}>{user?.role || 'Bus Driver'}</Text>
            <View style={styles.statusContainer}>
              <Icon
                name={user?.isActive ? 'check-circle' : 'cancel'}
                size={16}
                color={user?.isActive ? colors.success : colors.error}
              />
              <Text
                style={[
                  styles.status,
                  {color: user?.isActive ? colors.success : colors.error},
                ]}>
                {user?.isActive ? 'Active' : 'Inactive'}
              </Text>
            </View>
          </View>
        </Card.Content>
      </Card>

      {/* Profile Details */}
      <Card style={styles.detailsCard}>
        <Card.Content>
          <Text style={styles.sectionTitle}>Profile Information</Text>
          
          {profileItems.map((item, index) => (
            <View key={index}>
              <View style={styles.profileItem}>
                <Icon name={item.icon} size={20} color={colors.textSecondary} />
                <View style={styles.profileItemContent}>
                  <Text style={styles.profileLabel}>{item.label}</Text>
                  <Text style={styles.profileValue}>{item.value}</Text>
                </View>
              </View>
              {index < profileItems.length - 1 && <Divider style={styles.divider} />}
            </View>
          ))}
        </Card.Content>
      </Card>

      {/* App Information */}
      <Card style={styles.appInfoCard}>
        <Card.Content>
          <Text style={styles.sectionTitle}>Application Information</Text>
          
          <View style={styles.appInfoItem}>
            <Icon name="info" size={20} color={colors.textSecondary} />
            <View style={styles.appInfoContent}>
              <Text style={styles.appInfoLabel}>Version</Text>
              <Text style={styles.appInfoValue}>1.0.0</Text>
            </View>
          </View>

          <Divider style={styles.divider} />

          <View style={styles.appInfoItem}>
            <Icon name="my-location" size={20} color={colors.textSecondary} />
            <View style={styles.appInfoContent}>
              <Text style={styles.appInfoLabel}>Location Tracking</Text>
              <Text style={[
                styles.appInfoValue,
                {color: isTracking ? colors.success : colors.error}
              ]}>
                {isTracking ? 'Active' : 'Inactive'}
              </Text>
            </View>
          </View>

          <Divider style={styles.divider} />

          <View style={styles.appInfoItem}>
            <Icon name="business" size={20} color={colors.textSecondary} />
            <View style={styles.appInfoContent}>
              <Text style={styles.appInfoLabel}>Company</Text>
              <Text style={styles.appInfoValue}>Guzo Sync Transport</Text>
            </View>
          </View>
        </Card.Content>
      </Card>

      {/* Actions */}
      <Card style={styles.actionsCard}>
        <Card.Content>
          <Text style={styles.sectionTitle}>Actions</Text>
          
          <Button
            title="Change Password"
            onPress={() => {
              // TODO: Implement change password functionality
              Alert.alert('Coming Soon', 'This feature will be available in a future update.');
            }}
            mode="outlined"
            icon="lock"
            style={styles.actionButton}
          />

          <Button
            title="Update Profile"
            onPress={() => {
              // TODO: Implement update profile functionality
              Alert.alert('Coming Soon', 'This feature will be available in a future update.');
            }}
            mode="outlined"
            icon="edit"
            style={styles.actionButton}
          />

          <Button
            title="Logout"
            onPress={handleLogout}
            loading={isLoading}
            disabled={isLoading}
            variant="danger"
            icon="logout"
            style={styles.logoutButton}
          />
        </Card.Content>
      </Card>

      {/* Footer */}
      <View style={styles.footer}>
        <Text style={styles.footerText}>
          Guzo Sync Bus Driver Application
        </Text>
        <Text style={styles.footerSubtext}>
          © 2024 Guzo Sync Transport. All rights reserved.
        </Text>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
    padding: spacing.md,
  },
  headerCard: {
    marginBottom: spacing.md,
    ...shadows.small,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  avatar: {
    backgroundColor: colors.primary,
    marginRight: spacing.md,
  },
  headerInfo: {
    flex: 1,
  },
  name: {
    ...typography.h3,
    color: colors.text,
    marginBottom: spacing.xs,
  },
  role: {
    ...typography.body2,
    color: colors.textSecondary,
    marginBottom: spacing.xs,
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.xs,
  },
  status: {
    ...typography.caption,
    fontWeight: '600',
  },
  detailsCard: {
    marginBottom: spacing.md,
    ...shadows.small,
  },
  sectionTitle: {
    ...typography.h3,
    color: colors.text,
    marginBottom: spacing.md,
  },
  profileItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: spacing.sm,
  },
  profileItemContent: {
    marginLeft: spacing.md,
    flex: 1,
  },
  profileLabel: {
    ...typography.caption,
    color: colors.textSecondary,
    marginBottom: spacing.xs,
  },
  profileValue: {
    ...typography.body2,
    color: colors.text,
  },
  divider: {
    marginVertical: spacing.xs,
  },
  appInfoCard: {
    marginBottom: spacing.md,
    ...shadows.small,
  },
  appInfoItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: spacing.sm,
  },
  appInfoContent: {
    marginLeft: spacing.md,
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  appInfoLabel: {
    ...typography.body2,
    color: colors.text,
  },
  appInfoValue: {
    ...typography.body2,
    color: colors.textSecondary,
    fontWeight: '600',
  },
  actionsCard: {
    marginBottom: spacing.md,
    ...shadows.small,
  },
  actionButton: {
    marginBottom: spacing.sm,
  },
  logoutButton: {
    marginTop: spacing.sm,
  },
  footer: {
    alignItems: 'center',
    paddingVertical: spacing.lg,
  },
  footerText: {
    ...typography.body2,
    color: colors.textSecondary,
    textAlign: 'center',
  },
  footerSubtext: {
    ...typography.caption,
    color: colors.textSecondary,
    textAlign: 'center',
    marginTop: spacing.xs,
  },
});

export default ProfileScreen;
