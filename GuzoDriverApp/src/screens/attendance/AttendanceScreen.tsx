import React, {useEffect, useState} from 'react';
import {View, StyleSheet, ScrollView, Alert} from 'react-native';
import {Text, Card, Chip} from 'react-native-paper';
import {useDispatch, useSelector} from 'react-redux';
import Icon from 'react-native-vector-icons/MaterialIcons';
import Toast from 'react-native-toast-message';

import {RootState, AppDispatch} from '@store/index';
import {markAttendance, getTodayAttendance} from '@store/slices/attendanceSlice';
import {getCurrentLocation} from '@store/slices/locationSlice';
import Button from '@components/ui/Button';
import {colors, spacing, typography, shadows} from '@theme/index';

const AttendanceScreen: React.FC = () => {
  const dispatch = useDispatch<AppDispatch>();
  const {todayAttendance, isLoading} = useSelector(
    (state: RootState) => state.attendance,
  );
  const {currentLocation} = useSelector((state: RootState) => state.location);
  const {user} = useSelector((state: RootState) => state.auth);

  const [isMarkingAttendance, setIsMarkingAttendance] = useState(false);

  useEffect(() => {
    dispatch(getTodayAttendance());
  }, [dispatch]);

  const handleMarkAttendance = async () => {
    try {
      setIsMarkingAttendance(true);

      // Get current location first
      if (!currentLocation) {
        await dispatch(getCurrentLocation()).unwrap();
      }

      // Show confirmation dialog
      Alert.alert(
        'Mark Attendance',
        'Are you sure you want to mark your attendance for today?',
        [
          {
            text: 'Cancel',
            style: 'cancel',
            onPress: () => setIsMarkingAttendance(false),
          },
          {
            text: 'Confirm',
            onPress: async () => {
              try {
                await dispatch(
                  markAttendance({
                    location: currentLocation || undefined,
                  }),
                ).unwrap();

                Toast.show({
                  type: 'success',
                  text1: 'Attendance Marked',
                  text2: 'Your attendance has been successfully recorded.',
                });
              } catch (error: any) {
                Toast.show({
                  type: 'error',
                  text1: 'Failed to Mark Attendance',
                  text2: error.message || 'Please try again.',
                });
              } finally {
                setIsMarkingAttendance(false);
              }
            },
          },
        ],
      );
    } catch (error: any) {
      Toast.show({
        type: 'error',
        text1: 'Location Error',
        text2: 'Unable to get your current location. Please try again.',
      });
      setIsMarkingAttendance(false);
    }
  };

  const getAttendanceStatusColor = (status: string) => {
    switch (status) {
      case 'present':
        return colors.success;
      case 'late':
        return colors.warning;
      case 'absent':
        return colors.error;
      default:
        return colors.textSecondary;
    }
  };

  const getAttendanceStatusIcon = (status: string) => {
    switch (status) {
      case 'present':
        return 'check-circle';
      case 'late':
        return 'schedule';
      case 'absent':
        return 'cancel';
      default:
        return 'help';
    }
  };

  const formatTime = (timeString: string) => {
    try {
      return new Date(timeString).toLocaleTimeString('en-US', {
        hour: '2-digit',
        minute: '2-digit',
        hour12: true,
      });
    } catch {
      return timeString;
    }
  };

  const formatDate = (dateString: string) => {
    try {
      return new Date(dateString).toLocaleDateString('en-US', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric',
      });
    } catch {
      return dateString;
    }
  };

  const canMarkAttendance = !todayAttendance || todayAttendance.status === 'absent';

  return (
    <ScrollView style={styles.container}>
      {/* Header */}
      <Card style={styles.headerCard}>
        <Card.Content>
          <Text style={styles.headerTitle}>Attendance</Text>
          <Text style={styles.headerSubtitle}>
            {formatDate(new Date().toISOString())}
          </Text>
        </Card.Content>
      </Card>

      {/* Today's Attendance Status */}
      <Card style={styles.statusCard}>
        <Card.Content>
          <View style={styles.statusHeader}>
            <Text style={styles.statusTitle}>Today's Status</Text>
            {todayAttendance ? (
              <Chip
                style={{
                  backgroundColor: getAttendanceStatusColor(todayAttendance.status),
                }}
                textStyle={{color: colors.textLight}}
                icon={getAttendanceStatusIcon(todayAttendance.status)}>
                {todayAttendance.status.toUpperCase()}
              </Chip>
            ) : (
              <Chip
                style={{backgroundColor: colors.textSecondary}}
                textStyle={{color: colors.textLight}}
                icon="help">
                NOT MARKED
              </Chip>
            )}
          </View>

          {todayAttendance ? (
            <View style={styles.attendanceDetails}>
              <View style={styles.detailRow}>
                <Icon name="login" size={20} color={colors.textSecondary} />
                <Text style={styles.detailLabel}>Check-in Time:</Text>
                <Text style={styles.detailValue}>
                  {formatTime(todayAttendance.checkInTime)}
                </Text>
              </View>

              {todayAttendance.checkOutTime && (
                <View style={styles.detailRow}>
                  <Icon name="logout" size={20} color={colors.textSecondary} />
                  <Text style={styles.detailLabel}>Check-out Time:</Text>
                  <Text style={styles.detailValue}>
                    {formatTime(todayAttendance.checkOutTime)}
                  </Text>
                </View>
              )}

              {todayAttendance.location && (
                <View style={styles.detailRow}>
                  <Icon name="location-on" size={20} color={colors.textSecondary} />
                  <Text style={styles.detailLabel}>Location:</Text>
                  <Text style={styles.detailValue}>
                    {todayAttendance.location.latitude.toFixed(4)}, {todayAttendance.location.longitude.toFixed(4)}
                  </Text>
                </View>
              )}
            </View>
          ) : (
            <View style={styles.noAttendanceContainer}>
              <Icon name="schedule" size={48} color={colors.textSecondary} />
              <Text style={styles.noAttendanceText}>
                You haven't marked your attendance today
              </Text>
            </View>
          )}
        </Card.Content>
      </Card>

      {/* Mark Attendance Button */}
      {canMarkAttendance && (
        <Card style={styles.actionCard}>
          <Card.Content>
            <Text style={styles.actionTitle}>Mark Your Attendance</Text>
            <Text style={styles.actionDescription}>
              Tap the button below to mark your attendance for today. Your current location will be recorded.
            </Text>

            {currentLocation && (
              <View style={styles.locationInfo}>
                <Icon name="my-location" size={16} color={colors.textSecondary} />
                <Text style={styles.locationText}>
                  Current location: {currentLocation.latitude.toFixed(4)}, {currentLocation.longitude.toFixed(4)}
                </Text>
              </View>
            )}

            <Button
              title="Mark Attendance"
              onPress={handleMarkAttendance}
              loading={isMarkingAttendance || isLoading}
              disabled={isMarkingAttendance || isLoading}
              icon="check-circle"
              style={styles.markButton}
            />
          </Card.Content>
        </Card>
      )}

      {/* Instructions */}
      <Card style={styles.instructionsCard}>
        <Card.Content>
          <Text style={styles.instructionsTitle}>Instructions</Text>
          <View style={styles.instructionsList}>
            <View style={styles.instructionItem}>
              <Icon name="schedule" size={16} color={colors.textSecondary} />
              <Text style={styles.instructionText}>
                Mark your attendance at the beginning of your shift
              </Text>
            </View>
            <View style={styles.instructionItem}>
              <Icon name="location-on" size={16} color={colors.textSecondary} />
              <Text style={styles.instructionText}>
                Ensure location services are enabled for accurate tracking
              </Text>
            </View>
            <View style={styles.instructionItem}>
              <Icon name="check-circle" size={16} color={colors.textSecondary} />
              <Text style={styles.instructionText}>
                Attendance can only be marked once per day
              </Text>
            </View>
          </View>
        </Card.Content>
      </Card>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
    padding: spacing.md,
  },
  headerCard: {
    marginBottom: spacing.md,
    ...shadows.small,
  },
  headerTitle: {
    ...typography.h2,
    color: colors.text,
    marginBottom: spacing.xs,
  },
  headerSubtitle: {
    ...typography.body2,
    color: colors.textSecondary,
  },
  statusCard: {
    marginBottom: spacing.md,
    ...shadows.small,
  },
  statusHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  statusTitle: {
    ...typography.h3,
    color: colors.text,
  },
  attendanceDetails: {
    gap: spacing.sm,
  },
  detailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.sm,
  },
  detailLabel: {
    ...typography.body2,
    color: colors.textSecondary,
    flex: 1,
  },
  detailValue: {
    ...typography.body2,
    color: colors.text,
    fontWeight: '600',
  },
  noAttendanceContainer: {
    alignItems: 'center',
    paddingVertical: spacing.lg,
  },
  noAttendanceText: {
    ...typography.body2,
    color: colors.textSecondary,
    textAlign: 'center',
    marginTop: spacing.sm,
  },
  actionCard: {
    marginBottom: spacing.md,
    ...shadows.small,
  },
  actionTitle: {
    ...typography.h3,
    color: colors.text,
    marginBottom: spacing.sm,
  },
  actionDescription: {
    ...typography.body2,
    color: colors.textSecondary,
    marginBottom: spacing.md,
  },
  locationInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.xs,
    marginBottom: spacing.md,
    padding: spacing.sm,
    backgroundColor: colors.background,
    borderRadius: 8,
  },
  locationText: {
    ...typography.caption,
    color: colors.textSecondary,
  },
  markButton: {
    marginTop: spacing.sm,
  },
  instructionsCard: {
    marginBottom: spacing.md,
    ...shadows.small,
  },
  instructionsTitle: {
    ...typography.h3,
    color: colors.text,
    marginBottom: spacing.md,
  },
  instructionsList: {
    gap: spacing.sm,
  },
  instructionItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    gap: spacing.sm,
  },
  instructionText: {
    ...typography.body2,
    color: colors.textSecondary,
    flex: 1,
  },
});

export default AttendanceScreen;
