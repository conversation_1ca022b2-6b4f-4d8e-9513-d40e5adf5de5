import React, {useEffect} from 'react';
import {View, StyleSheet, ScrollView, RefreshControl} from 'react-native';
import {Text, Card, Chip} from 'react-native-paper';
import {useDispatch, useSelector} from 'react-redux';
import Icon from 'react-native-vector-icons/MaterialIcons';

import {RootState, AppDispatch} from '@store/index';
import {getDriverSchedules} from '@store/slices/routeSlice';
import {getTodayAttendance} from '@store/slices/attendanceSlice';
import {startLocationTracking} from '@store/slices/locationSlice';
import Button from '@components/ui/Button';
import {colors, spacing, typography, shadows} from '@theme/index';

const DashboardScreen: React.FC = () => {
  const dispatch = useDispatch<AppDispatch>();
  const {user} = useSelector((state: RootState) => state.auth);
  const {currentSchedule, isLoading: routeLoading} = useSelector(
    (state: RootState) => state.route,
  );
  const {todayAttendance, isLoading: attendanceLoading} = useSelector(
    (state: RootState) => state.attendance,
  );
  const {currentLocation, isTracking} = useSelector(
    (state: RootState) => state.location,
  );

  const [refreshing, setRefreshing] = React.useState(false);

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = () => {
    dispatch(getDriverSchedules());
    dispatch(getTodayAttendance());
  };

  const onRefresh = React.useCallback(() => {
    setRefreshing(true);
    loadDashboardData();
    setTimeout(() => setRefreshing(false), 1000);
  }, []);

  const handleStartTracking = () => {
    dispatch(startLocationTracking());
  };

  const getScheduleStatusColor = (status: string) => {
    switch (status) {
      case 'scheduled':
        return colors.warning;
      case 'in_progress':
        return colors.primary;
      case 'completed':
        return colors.success;
      case 'cancelled':
        return colors.error;
      default:
        return colors.textSecondary;
    }
  };

  const getAttendanceStatusColor = (status: string) => {
    switch (status) {
      case 'present':
        return colors.success;
      case 'late':
        return colors.warning;
      case 'absent':
        return colors.error;
      default:
        return colors.textSecondary;
    }
  };

  return (
    <ScrollView
      style={styles.container}
      refreshControl={
        <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
      }>
      {/* Welcome Section */}
      <Card style={styles.welcomeCard}>
        <Card.Content>
          <Text style={styles.welcomeText}>
            Welcome back, {user?.firstName || 'Driver'}!
          </Text>
          <Text style={styles.dateText}>
            {new Date().toLocaleDateString('en-US', {
              weekday: 'long',
              year: 'numeric',
              month: 'long',
              day: 'numeric',
            })}
          </Text>
        </Card.Content>
      </Card>

      {/* Quick Stats */}
      <View style={styles.statsContainer}>
        <Card style={styles.statCard}>
          <Card.Content style={styles.statContent}>
            <Icon name="schedule" size={24} color={colors.primary} />
            <Text style={styles.statLabel}>Today's Route</Text>
            <Text style={styles.statValue}>
              {currentSchedule?.route?.name || 'No route assigned'}
            </Text>
          </Card.Content>
        </Card>

        <Card style={styles.statCard}>
          <Card.Content style={styles.statContent}>
            <Icon name="access-time" size={24} color={colors.success} />
            <Text style={styles.statLabel}>Attendance</Text>
            <Text style={styles.statValue}>
              {todayAttendance?.status || 'Not marked'}
            </Text>
          </Card.Content>
        </Card>
      </View>

      {/* Current Schedule */}
      {currentSchedule && (
        <Card style={styles.scheduleCard}>
          <Card.Content>
            <View style={styles.scheduleHeader}>
              <Text style={styles.scheduleTitle}>Current Schedule</Text>
              <Chip
                style={{
                  backgroundColor: getScheduleStatusColor(currentSchedule.status),
                }}
                textStyle={{color: colors.textLight}}>
                {currentSchedule.status.replace('_', ' ').toUpperCase()}
              </Chip>
            </View>

            <View style={styles.scheduleDetails}>
              <View style={styles.scheduleRow}>
                <Icon name="route" size={20} color={colors.textSecondary} />
                <Text style={styles.scheduleText}>
                  {currentSchedule.route.name}
                </Text>
              </View>

              <View style={styles.scheduleRow}>
                <Icon name="schedule" size={20} color={colors.textSecondary} />
                <Text style={styles.scheduleText}>
                  {currentSchedule.startTime} - {currentSchedule.endTime}
                </Text>
              </View>

              <View style={styles.scheduleRow}>
                <Icon name="location-on" size={20} color={colors.textSecondary} />
                <Text style={styles.scheduleText}>
                  {currentSchedule.route.stops?.length || 0} stops
                </Text>
              </View>
            </View>
          </Card.Content>
        </Card>
      )}

      {/* Attendance Status */}
      {todayAttendance && (
        <Card style={styles.attendanceCard}>
          <Card.Content>
            <View style={styles.attendanceHeader}>
              <Text style={styles.attendanceTitle}>Today's Attendance</Text>
              <Chip
                style={{
                  backgroundColor: getAttendanceStatusColor(todayAttendance.status),
                }}
                textStyle={{color: colors.textLight}}>
                {todayAttendance.status.toUpperCase()}
              </Chip>
            </View>

            <View style={styles.attendanceDetails}>
              <View style={styles.attendanceRow}>
                <Icon name="login" size={20} color={colors.textSecondary} />
                <Text style={styles.attendanceText}>
                  Check-in: {todayAttendance.checkInTime}
                </Text>
              </View>

              {todayAttendance.checkOutTime && (
                <View style={styles.attendanceRow}>
                  <Icon name="logout" size={20} color={colors.textSecondary} />
                  <Text style={styles.attendanceText}>
                    Check-out: {todayAttendance.checkOutTime}
                  </Text>
                </View>
              )}
            </View>
          </Card.Content>
        </Card>
      )}

      {/* Location Tracking */}
      <Card style={styles.trackingCard}>
        <Card.Content>
          <View style={styles.trackingHeader}>
            <Text style={styles.trackingTitle}>Location Tracking</Text>
            <Chip
              style={{
                backgroundColor: isTracking ? colors.success : colors.textSecondary,
              }}
              textStyle={{color: colors.textLight}}>
              {isTracking ? 'ACTIVE' : 'INACTIVE'}
            </Chip>
          </View>

          {currentLocation && (
            <View style={styles.locationInfo}>
              <Text style={styles.locationText}>
                Last update: {new Date(currentLocation.timestamp || 0).toLocaleTimeString()}
              </Text>
              <Text style={styles.locationText}>
                Accuracy: ±{Math.round(currentLocation.accuracy || 0)}m
              </Text>
            </View>
          )}

          {!isTracking && (
            <Button
              title="Start Location Tracking"
              onPress={handleStartTracking}
              icon="my-location"
              style={styles.trackingButton}
            />
          )}
        </Card.Content>
      </Card>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
    padding: spacing.md,
  },
  welcomeCard: {
    marginBottom: spacing.md,
    ...shadows.small,
  },
  welcomeText: {
    ...typography.h3,
    color: colors.text,
    marginBottom: spacing.xs,
  },
  dateText: {
    ...typography.body2,
    color: colors.textSecondary,
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: spacing.md,
  },
  statCard: {
    flex: 1,
    marginHorizontal: spacing.xs,
    ...shadows.small,
  },
  statContent: {
    alignItems: 'center',
  },
  statLabel: {
    ...typography.caption,
    color: colors.textSecondary,
    marginTop: spacing.xs,
    textAlign: 'center',
  },
  statValue: {
    ...typography.body2,
    color: colors.text,
    fontWeight: '600',
    marginTop: spacing.xs,
    textAlign: 'center',
  },
  scheduleCard: {
    marginBottom: spacing.md,
    ...shadows.small,
  },
  scheduleHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  scheduleTitle: {
    ...typography.h3,
    color: colors.text,
  },
  scheduleDetails: {
    gap: spacing.sm,
  },
  scheduleRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.sm,
  },
  scheduleText: {
    ...typography.body2,
    color: colors.text,
  },
  attendanceCard: {
    marginBottom: spacing.md,
    ...shadows.small,
  },
  attendanceHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  attendanceTitle: {
    ...typography.h3,
    color: colors.text,
  },
  attendanceDetails: {
    gap: spacing.sm,
  },
  attendanceRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.sm,
  },
  attendanceText: {
    ...typography.body2,
    color: colors.text,
  },
  trackingCard: {
    marginBottom: spacing.md,
    ...shadows.small,
  },
  trackingHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  trackingTitle: {
    ...typography.h3,
    color: colors.text,
  },
  locationInfo: {
    marginBottom: spacing.md,
  },
  locationText: {
    ...typography.body2,
    color: colors.textSecondary,
  },
  trackingButton: {
    marginTop: spacing.sm,
  },
});

export default DashboardScreen;
