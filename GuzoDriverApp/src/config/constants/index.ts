// API Configuration
export const API_CONFIG = {
  BASE_URL: 'https://api.guzosync.com', // Replace with actual backend URL
  TIMEOUT: 10000,
  RETRY_ATTEMPTS: 3,
};

// Mapbox Configuration
export const MAPBOX_CONFIG = {
  ACCESS_TOKEN: 'YOUR_MAPBOX_ACCESS_TOKEN', // Replace with actual Mapbox token
  STYLE_URL: 'mapbox://styles/mapbox/streets-v11',
  DEFAULT_ZOOM: 12,
  USER_LOCATION_ZOOM: 15,
};

// Location Configuration
export const LOCATION_CONFIG = {
  UPDATE_INTERVAL: 5000, // 5 seconds
  DISTANCE_FILTER: 10, // 10 meters
  ACCURACY_THRESHOLD: 50, // 50 meters
  BUS_STOP_PROXIMITY: 50, // 50 meters
};

// App Configuration
export const APP_CONFIG = {
  NAME: 'Guzo Driver App',
  VERSION: '1.0.0',
  COMPANY: 'Guzo Sync Transport',
  SUPPORT_EMAIL: '<EMAIL>',
  SUPPORT_PHONE: '+251-**********',
};

// Storage Keys
export const STORAGE_KEYS = {
  AUTH_TOKEN: 'auth_token',
  USER_DATA: 'user_data',
  LAST_LOCATION: 'last_location',
  APP_SETTINGS: 'app_settings',
};

// Default Coordinates (Addis Ababa, Ethiopia)
export const DEFAULT_COORDINATES = {
  latitude: 9.0579,
  longitude: 38.7469,
};

// Time Formats
export const TIME_FORMATS = {
  TIME_12H: 'h:mm A',
  TIME_24H: 'HH:mm',
  DATE_SHORT: 'MMM DD, YYYY',
  DATE_LONG: 'MMMM DD, YYYY',
  DATETIME: 'MMM DD, YYYY h:mm A',
};

// Error Messages
export const ERROR_MESSAGES = {
  NETWORK_ERROR: 'Network connection error. Please check your internet connection.',
  LOCATION_PERMISSION_DENIED: 'Location permission is required for this app to work properly.',
  LOCATION_UNAVAILABLE: 'Unable to get your current location. Please try again.',
  LOGIN_FAILED: 'Login failed. Please check your credentials and try again.',
  ATTENDANCE_ALREADY_MARKED: 'You have already marked your attendance for today.',
  INVALID_CREDENTIALS: 'Invalid username or password.',
  SESSION_EXPIRED: 'Your session has expired. Please login again.',
};

// Success Messages
export const SUCCESS_MESSAGES = {
  LOGIN_SUCCESS: 'Login successful!',
  ATTENDANCE_MARKED: 'Attendance marked successfully!',
  LOCATION_UPDATED: 'Location updated successfully!',
  PROFILE_UPDATED: 'Profile updated successfully!',
};

// Route Status
export const ROUTE_STATUS = {
  SCHEDULED: 'scheduled',
  IN_PROGRESS: 'in_progress',
  COMPLETED: 'completed',
  CANCELLED: 'cancelled',
} as const;

// Attendance Status
export const ATTENDANCE_STATUS = {
  PRESENT: 'present',
  ABSENT: 'absent',
  LATE: 'late',
} as const;

// User Roles
export const USER_ROLES = {
  DRIVER: 'driver',
  ADMIN: 'admin',
  SUPERVISOR: 'supervisor',
} as const;
