import React from 'react';
import {StyleSheet, ViewStyle} from 'react-native';
import {Button as PaperButton} from 'react-native-paper';
import {colors, spacing, typography} from '@theme/index';

interface ButtonProps {
  title: string;
  onPress: () => void;
  mode?: 'text' | 'outlined' | 'contained';
  disabled?: boolean;
  loading?: boolean;
  icon?: string;
  style?: ViewStyle;
  variant?: 'primary' | 'secondary' | 'danger';
}

const Button: React.FC<ButtonProps> = ({
  title,
  onPress,
  mode = 'contained',
  disabled = false,
  loading = false,
  icon,
  style,
  variant = 'primary',
}) => {
  const getButtonColor = () => {
    switch (variant) {
      case 'secondary':
        return colors.secondary;
      case 'danger':
        return colors.error;
      default:
        return colors.primary;
    }
  };

  return (
    <PaperButton
      mode={mode}
      onPress={onPress}
      disabled={disabled}
      loading={loading}
      icon={icon}
      buttonColor={mode === 'contained' ? getButtonColor() : undefined}
      textColor={
        mode === 'contained' 
          ? colors.textLight 
          : getButtonColor()
      }
      style={[styles.button, style]}
      labelStyle={styles.label}
      contentStyle={styles.content}>
      {title}
    </PaperButton>
  );
};

const styles = StyleSheet.create({
  button: {
    borderRadius: 8,
    marginVertical: spacing.xs,
  },
  content: {
    paddingVertical: spacing.xs,
    paddingHorizontal: spacing.md,
  },
  label: {
    ...typography.button,
  },
});

export default Button;
