// Auth Types
export interface LoginCredentials {
  username: string;
  password: string;
}

export interface User {
  id: string;
  username: string;
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  role: string;
  isActive: boolean;
}

export interface AuthState {
  user: User | null;
  token: string | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
}

// Route Types
export interface BusStop {
  id: string;
  name: string;
  latitude: number;
  longitude: number;
  order: number;
}

export interface Route {
  id: string;
  name: string;
  description: string;
  stops: BusStop[];
  isActive: boolean;
}

export interface Schedule {
  id: string;
  routeId: string;
  route: Route;
  startTime: string;
  endTime: string;
  date: string;
  status: 'scheduled' | 'in_progress' | 'completed' | 'cancelled';
}

// Location Types
export interface Location {
  latitude: number;
  longitude: number;
  accuracy?: number;
  timestamp?: number;
}

export interface LocationState {
  currentLocation: Location | null;
  isTracking: boolean;
  error: string | null;
}

// Attendance Types
export interface AttendanceRecord {
  id: string;
  driverId: string;
  date: string;
  checkInTime: string;
  checkOutTime?: string;
  status: 'present' | 'absent' | 'late';
  location?: Location;
}

export interface AttendanceState {
  todayAttendance: AttendanceRecord | null;
  isLoading: boolean;
  error: string | null;
}

// API Response Types
export interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
  error?: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

// Navigation Types
export type RootStackParamList = {
  Auth: undefined;
  Main: undefined;
};

export type AuthStackParamList = {
  Login: undefined;
};

export type MainTabParamList = {
  Dashboard: undefined;
  Map: undefined;
  Attendance: undefined;
  Profile: undefined;
};

// Error Types
export interface ApiError {
  message: string;
  status?: number;
  code?: string;
}

// Common Types
export interface LoadingState {
  isLoading: boolean;
  error: string | null;
}
