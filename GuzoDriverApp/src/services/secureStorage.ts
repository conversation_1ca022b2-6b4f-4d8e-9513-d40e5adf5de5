import * as Keychain from 'react-native-keychain';

const SERVICE_NAME = 'GuzoDriverApp';
const TOKEN_KEY = 'auth_token';
const USER_KEY = 'user_data';

export const secureStorage = {
  // Token management
  setToken: async (token: string): Promise<void> => {
    try {
      await Keychain.setInternetCredentials(
        `${SERVICE_NAME}_${TOKEN_KEY}`,
        TOKEN_KEY,
        token
      );
    } catch (error) {
      console.error('Error storing token:', error);
      throw new Error('Failed to store authentication token');
    }
  },

  getToken: async (): Promise<string | null> => {
    try {
      const credentials = await Keychain.getInternetCredentials(
        `${SERVICE_NAME}_${TOKEN_KEY}`
      );
      
      if (credentials && credentials.password) {
        return credentials.password;
      }
      return null;
    } catch (error) {
      console.error('Error retrieving token:', error);
      return null;
    }
  },

  removeToken: async (): Promise<void> => {
    try {
      await Keychain.resetInternetCredentials(`${SERVICE_NAME}_${TOKEN_KEY}`);
    } catch (error) {
      console.error('Error removing token:', error);
    }
  },

  // User data management
  setUserData: async (userData: any): Promise<void> => {
    try {
      await Keychain.setInternetCredentials(
        `${SERVICE_NAME}_${USER_KEY}`,
        USER_KEY,
        JSON.stringify(userData)
      );
    } catch (error) {
      console.error('Error storing user data:', error);
      throw new Error('Failed to store user data');
    }
  },

  getUserData: async (): Promise<any | null> => {
    try {
      const credentials = await Keychain.getInternetCredentials(
        `${SERVICE_NAME}_${USER_KEY}`
      );
      
      if (credentials && credentials.password) {
        return JSON.parse(credentials.password);
      }
      return null;
    } catch (error) {
      console.error('Error retrieving user data:', error);
      return null;
    }
  },

  removeUserData: async (): Promise<void> => {
    try {
      await Keychain.resetInternetCredentials(`${SERVICE_NAME}_${USER_KEY}`);
    } catch (error) {
      console.error('Error removing user data:', error);
    }
  },

  // Clear all stored data
  clearAll: async (): Promise<void> => {
    try {
      await Promise.all([
        secureStorage.removeToken(),
        secureStorage.removeUserData(),
      ]);
    } catch (error) {
      console.error('Error clearing all data:', error);
    }
  },

  // Check if keychain is available
  isKeychainAvailable: async (): Promise<boolean> => {
    try {
      const biometryType = await Keychain.getSupportedBiometryType();
      return true;
    } catch (error) {
      return false;
    }
  },
};
