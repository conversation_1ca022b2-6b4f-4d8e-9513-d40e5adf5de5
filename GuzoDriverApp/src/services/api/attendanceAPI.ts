import {apiClient} from './apiClient';
import {AttendanceRecord, Location, ApiResponse, PaginatedResponse} from '@types/index';

interface MarkAttendanceRequest {
  location?: Location;
}

export const attendanceAPI = {
  // Mark attendance (check-in)
  markAttendance: async (data: MarkAttendanceRequest): Promise<ApiResponse<AttendanceRecord>> => {
    const response = await apiClient.post<ApiResponse<AttendanceRecord>>(
      '/api/drivers/attendance',
      data
    );
    return response.data;
  },

  // Get today's attendance
  getTodayAttendance: async (): Promise<ApiResponse<AttendanceRecord>> => {
    const today = new Date().toISOString().split('T')[0];
    const response = await apiClient.get<ApiResponse<AttendanceRecord>>(
      `/api/drivers/attendance/today?date=${today}`
    );
    return response.data;
  },

  // Get attendance history
  getAttendanceHistory: async (params?: {
    page?: number;
    limit?: number;
    startDate?: string;
    endDate?: string;
  }): Promise<ApiResponse<PaginatedResponse<AttendanceRecord>>> => {
    const queryParams = new URLSearchParams();
    
    if (params?.page) queryParams.append('page', params.page.toString());
    if (params?.limit) queryParams.append('limit', params.limit.toString());
    if (params?.startDate) queryParams.append('startDate', params.startDate);
    if (params?.endDate) queryParams.append('endDate', params.endDate);

    const response = await apiClient.get<ApiResponse<PaginatedResponse<AttendanceRecord>>>(
      `/api/drivers/attendance/history?${queryParams.toString()}`
    );
    return response.data;
  },

  // Check out (end shift)
  checkOut: async (data: {location?: Location}): Promise<ApiResponse<AttendanceRecord>> => {
    const response = await apiClient.patch<ApiResponse<AttendanceRecord>>(
      '/api/drivers/attendance/checkout',
      data
    );
    return response.data;
  },

  // Get attendance statistics
  getAttendanceStats: async (params?: {
    month?: string;
    year?: string;
  }): Promise<ApiResponse<{
    totalDays: number;
    presentDays: number;
    absentDays: number;
    lateDays: number;
    attendanceRate: number;
  }>> => {
    const queryParams = new URLSearchParams();
    
    if (params?.month) queryParams.append('month', params.month);
    if (params?.year) queryParams.append('year', params.year);

    const response = await apiClient.get(
      `/api/drivers/attendance/stats?${queryParams.toString()}`
    );
    return response.data;
  },
};
