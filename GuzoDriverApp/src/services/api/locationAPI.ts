import {apiClient} from './apiClient';
import {Location, ApiResponse} from '@types/index';

export const locationAPI = {
  // Update driver's current location
  updateLocation: async (location: Location): Promise<ApiResponse<void>> => {
    const response = await apiClient.post<ApiResponse<void>>(
      '/internal/tracker/update-bus-location',
      {
        latitude: location.latitude,
        longitude: location.longitude,
        accuracy: location.accuracy,
        timestamp: location.timestamp || Date.now(),
      }
    );
    return response.data;
  },

  // Get driver's location history
  getLocationHistory: async (params?: {
    startDate?: string;
    endDate?: string;
    limit?: number;
  }): Promise<ApiResponse<Location[]>> => {
    const queryParams = new URLSearchParams();
    
    if (params?.startDate) queryParams.append('startDate', params.startDate);
    if (params?.endDate) queryParams.append('endDate', params.endDate);
    if (params?.limit) queryParams.append('limit', params.limit.toString());

    const response = await apiClient.get<ApiResponse<Location[]>>(
      `/api/drivers/location/history?${queryParams.toString()}`
    );
    return response.data;
  },

  // Start trip tracking
  startTrip: async (scheduleId: string, location: Location): Promise<ApiResponse<void>> => {
    const response = await apiClient.post<ApiResponse<void>>(
      '/api/drivers/trips/start',
      {
        scheduleId,
        startLocation: location,
        timestamp: Date.now(),
      }
    );
    return response.data;
  },

  // End trip tracking
  endTrip: async (scheduleId: string, location: Location): Promise<ApiResponse<void>> => {
    const response = await apiClient.post<ApiResponse<void>>(
      '/api/drivers/trips/end',
      {
        scheduleId,
        endLocation: location,
        timestamp: Date.now(),
      }
    );
    return response.data;
  },
};
