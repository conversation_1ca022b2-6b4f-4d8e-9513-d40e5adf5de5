import {apiClient} from './apiClient';
import {Route, Schedule, ApiResponse} from '@types/index';

export const routeAPI = {
  // Get driver's assigned schedules
  getDriverSchedules: async (params?: {
    date?: string;
    status?: string;
  }): Promise<ApiResponse<Schedule[]>> => {
    const queryParams = new URLSearchParams();
    
    if (params?.date) queryParams.append('date', params.date);
    if (params?.status) queryParams.append('status', params.status);

    const response = await apiClient.get<ApiResponse<Schedule[]>>(
      `/api/drivers/schedules?${queryParams.toString()}`
    );
    return response.data;
  },

  // Get specific route details
  getRouteDetails: async (routeId: string): Promise<ApiResponse<Route>> => {
    const response = await apiClient.get<ApiResponse<Route>>(
      `/api/drivers/routes/${routeId}`
    );
    return response.data;
  },

  // Get route schedule details
  getRouteSchedule: async (routeId: string, params?: {
    date?: string;
  }): Promise<ApiResponse<Schedule>> => {
    const queryParams = new URLSearchParams();
    
    if (params?.date) queryParams.append('date', params.date);

    const response = await apiClient.get<ApiResponse<Schedule>>(
      `/api/drivers/routes/${routeId}/schedule?${queryParams.toString()}`
    );
    return response.data;
  },

  // Update schedule status
  updateScheduleStatus: async (
    scheduleId: string,
    status: 'in_progress' | 'completed' | 'cancelled'
  ): Promise<ApiResponse<Schedule>> => {
    const response = await apiClient.patch<ApiResponse<Schedule>>(
      `/api/drivers/schedules/${scheduleId}/status`,
      { status }
    );
    return response.data;
  },

  // Get all available routes (for reference)
  getAllRoutes: async (): Promise<ApiResponse<Route[]>> => {
    const response = await apiClient.get<ApiResponse<Route[]>>('/api/routes');
    return response.data;
  },
};
