import {apiClient} from './apiClient';
import {LoginCredentials, User, ApiResponse} from '@types/index';

interface LoginResponse {
  user: User;
  token: string;
}

export const authAPI = {
  // Login driver
  login: async (credentials: LoginCredentials): Promise<ApiResponse<LoginResponse>> => {
    const response = await apiClient.post<ApiResponse<LoginResponse>>(
      '/api/accounts/login',
      credentials
    );
    return response.data;
  },

  // Logout driver
  logout: async (): Promise<ApiResponse<void>> => {
    const response = await apiClient.post<ApiResponse<void>>('/api/accounts/logout');
    return response.data;
  },

  // Get driver profile
  getProfile: async (): Promise<ApiResponse<User>> => {
    const response = await apiClient.get<ApiResponse<User>>('/api/accounts/profile');
    return response.data;
  },

  // Refresh token (if backend supports it)
  refreshToken: async (): Promise<ApiResponse<{token: string}>> => {
    const response = await apiClient.post<ApiResponse<{token: string}>>('/api/accounts/refresh');
    return response.data;
  },

  // Update profile
  updateProfile: async (profileData: Partial<User>): Promise<ApiResponse<User>> => {
    const response = await apiClient.put<ApiResponse<User>>('/api/accounts/profile', profileData);
    return response.data;
  },

  // Change password
  changePassword: async (passwordData: {
    currentPassword: string;
    newPassword: string;
  }): Promise<ApiResponse<void>> => {
    const response = await apiClient.post<ApiResponse<void>>(
      '/api/accounts/change-password',
      passwordData
    );
    return response.data;
  },
};
