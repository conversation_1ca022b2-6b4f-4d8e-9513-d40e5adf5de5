import axios, {AxiosInstance, AxiosRequestConfig, AxiosResponse} from 'axios';
import {secureStorage} from '@services/secureStorage';

// Base URL for the Guzo Sync Backend
const BASE_URL = 'https://api.guzosync.com'; // Replace with actual backend URL

class ApiClient {
  private client: AxiosInstance;

  constructor() {
    this.client = axios.create({
      baseURL: BASE_URL,
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    this.setupInterceptors();
  }

  private setupInterceptors() {
    // Request interceptor to add auth token
    this.client.interceptors.request.use(
      async (config) => {
        const token = await secureStorage.getToken();
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // Response interceptor for error handling
    this.client.interceptors.response.use(
      (response: AxiosResponse) => {
        return response;
      },
      async (error) => {
        if (error.response?.status === 401) {
          // Token expired or invalid
          await secureStorage.removeToken();
          // You might want to dispatch a logout action here
        }
        return Promise.reject(error);
      }
    );
  }

  async get<T>(url: string, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> {
    return this.client.get<T>(url, config);
  }

  async post<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> {
    return this.client.post<T>(url, data, config);
  }

  async put<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> {
    return this.client.put<T>(url, data, config);
  }

  async patch<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> {
    return this.client.patch<T>(url, data, config);
  }

  async delete<T>(url: string, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> {
    return this.client.delete<T>(url, config);
  }

  // Method to update base URL if needed
  updateBaseURL(newBaseURL: string) {
    this.client.defaults.baseURL = newBaseURL;
  }

  // Method to set custom headers
  setHeader(key: string, value: string) {
    this.client.defaults.headers.common[key] = value;
  }

  // Method to remove custom headers
  removeHeader(key: string) {
    delete this.client.defaults.headers.common[key];
  }
}

export const apiClient = new ApiClient();
