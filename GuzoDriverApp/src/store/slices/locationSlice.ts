import {createSlice, createAsyncThunk, PayloadAction} from '@reduxjs/toolkit';
import {LocationState, Location, ApiError} from '@types/index';
import {locationService} from '@services/locationService';
import {locationAPI} from '@services/api/locationAPI';

const initialState: LocationState = {
  currentLocation: null,
  isTracking: false,
  error: null,
};

// Async thunks
export const startLocationTracking = createAsyncThunk<
  void,
  void,
  {rejectValue: ApiError}
>('location/startTracking', async (_, {dispatch, rejectWithValue}) => {
  try {
    await locationService.startTracking((location: Location) => {
      dispatch(updateLocation(location));
      // Send location to backend
      locationAPI.updateLocation(location).catch(console.error);
    });
  } catch (error: any) {
    return rejectWithValue({
      message: error.message || 'Failed to start location tracking',
    });
  }
});

export const stopLocationTracking = createAsyncThunk<
  void,
  void,
  {rejectValue: ApiError}
>('location/stopTracking', async (_, {rejectWithValue}) => {
  try {
    await locationService.stopTracking();
  } catch (error: any) {
    return rejectWithValue({
      message: error.message || 'Failed to stop location tracking',
    });
  }
});

export const getCurrentLocation = createAsyncThunk<
  Location,
  void,
  {rejectValue: ApiError}
>('location/getCurrentLocation', async (_, {rejectWithValue}) => {
  try {
    const location = await locationService.getCurrentLocation();
    return location;
  } catch (error: any) {
    return rejectWithValue({
      message: error.message || 'Failed to get current location',
    });
  }
});

const locationSlice = createSlice({
  name: 'location',
  initialState,
  reducers: {
    updateLocation: (state, action: PayloadAction<Location>) => {
      state.currentLocation = action.payload;
      state.error = null;
    },
    clearLocationError: state => {
      state.error = null;
    },
  },
  extraReducers: builder => {
    // Start tracking
    builder
      .addCase(startLocationTracking.pending, state => {
        state.error = null;
      })
      .addCase(startLocationTracking.fulfilled, state => {
        state.isTracking = true;
        state.error = null;
      })
      .addCase(startLocationTracking.rejected, (state, action) => {
        state.isTracking = false;
        state.error = action.payload?.message || 'Failed to start tracking';
      });

    // Stop tracking
    builder
      .addCase(stopLocationTracking.fulfilled, state => {
        state.isTracking = false;
        state.error = null;
      })
      .addCase(stopLocationTracking.rejected, (state, action) => {
        state.error = action.payload?.message || 'Failed to stop tracking';
      });

    // Get current location
    builder
      .addCase(getCurrentLocation.fulfilled, (state, action) => {
        state.currentLocation = action.payload;
        state.error = null;
      })
      .addCase(getCurrentLocation.rejected, (state, action) => {
        state.error = action.payload?.message || 'Failed to get location';
      });
  },
});

export const {updateLocation, clearLocationError} = locationSlice.actions;
export default locationSlice.reducer;
