import {createSlice, createAsyncThunk} from '@reduxjs/toolkit';
import {Schedule, Route, ApiError} from '@types/index';
import {routeAPI} from '@services/api/routeAPI';

interface RouteState {
  currentSchedule: Schedule | null;
  schedules: Schedule[];
  routes: Route[];
  isLoading: boolean;
  error: string | null;
}

const initialState: RouteState = {
  currentSchedule: null,
  schedules: [],
  routes: [],
  isLoading: false,
  error: null,
};

// Async thunks
export const getDriverSchedules = createAsyncThunk<
  Schedule[],
  void,
  {rejectValue: ApiError}
>('route/getSchedules', async (_, {rejectWithValue}) => {
  try {
    const response = await routeAPI.getDriverSchedules();
    return response.data;
  } catch (error: any) {
    return rejectWithValue({
      message: error.response?.data?.message || 'Failed to get schedules',
      status: error.response?.status,
    });
  }
});

export const getRouteDetails = createAsyncThunk<
  Route,
  string,
  {rejectValue: ApiError}
>('route/getDetails', async (routeId, {rejectWithValue}) => {
  try {
    const response = await routeAPI.getRouteDetails(routeId);
    return response.data;
  } catch (error: any) {
    return rejectWithValue({
      message: error.response?.data?.message || 'Failed to get route details',
      status: error.response?.status,
    });
  }
});

const routeSlice = createSlice({
  name: 'route',
  initialState,
  reducers: {
    setCurrentSchedule: (state, action) => {
      state.currentSchedule = action.payload;
    },
    clearRouteError: state => {
      state.error = null;
    },
  },
  extraReducers: builder => {
    // Get schedules
    builder
      .addCase(getDriverSchedules.pending, state => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(getDriverSchedules.fulfilled, (state, action) => {
        state.isLoading = false;
        state.schedules = action.payload;
        // Set current schedule to today's or next upcoming
        const today = new Date().toISOString().split('T')[0];
        const todaySchedule = action.payload.find(s => s.date === today);
        if (todaySchedule) {
          state.currentSchedule = todaySchedule;
        } else if (action.payload.length > 0) {
          state.currentSchedule = action.payload[0];
        }
        state.error = null;
      })
      .addCase(getDriverSchedules.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload?.message || 'Failed to get schedules';
      });

    // Get route details
    builder
      .addCase(getRouteDetails.fulfilled, (state, action) => {
        const existingIndex = state.routes.findIndex(r => r.id === action.payload.id);
        if (existingIndex >= 0) {
          state.routes[existingIndex] = action.payload;
        } else {
          state.routes.push(action.payload);
        }
      });
  },
});

export const {setCurrentSchedule, clearRouteError} = routeSlice.actions;
export default routeSlice.reducer;
