import {createSlice, createAsyncThunk} from '@reduxjs/toolkit';
import {AttendanceState, AttendanceRecord, Location, ApiError} from '@types/index';
import {attendanceAPI} from '@services/api/attendanceAPI';

const initialState: AttendanceState = {
  todayAttendance: null,
  isLoading: false,
  error: null,
};

// Async thunks
export const markAttendance = createAsyncThunk<
  AttendanceRecord,
  {location?: Location},
  {rejectValue: ApiError}
>('attendance/mark', async ({location}, {rejectWithValue}) => {
  try {
    const response = await attendanceAPI.markAttendance({location});
    return response.data;
  } catch (error: any) {
    return rejectWithValue({
      message: error.response?.data?.message || 'Failed to mark attendance',
      status: error.response?.status,
    });
  }
});

export const getTodayAttendance = createAsyncThunk<
  AttendanceRecord | null,
  void,
  {rejectValue: ApiError}
>('attendance/getToday', async (_, {rejectWithValue}) => {
  try {
    const response = await attendanceAPI.getTodayAttendance();
    return response.data;
  } catch (error: any) {
    if (error.response?.status === 404) {
      return null; // No attendance record for today
    }
    return rejectWithValue({
      message: error.response?.data?.message || 'Failed to get attendance',
      status: error.response?.status,
    });
  }
});

const attendanceSlice = createSlice({
  name: 'attendance',
  initialState,
  reducers: {
    clearAttendanceError: state => {
      state.error = null;
    },
  },
  extraReducers: builder => {
    // Mark attendance
    builder
      .addCase(markAttendance.pending, state => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(markAttendance.fulfilled, (state, action) => {
        state.isLoading = false;
        state.todayAttendance = action.payload;
        state.error = null;
      })
      .addCase(markAttendance.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload?.message || 'Failed to mark attendance';
      });

    // Get today's attendance
    builder
      .addCase(getTodayAttendance.pending, state => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(getTodayAttendance.fulfilled, (state, action) => {
        state.isLoading = false;
        state.todayAttendance = action.payload;
        state.error = null;
      })
      .addCase(getTodayAttendance.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload?.message || 'Failed to get attendance';
      });
  },
});

export const {clearAttendanceError} = attendanceSlice.actions;
export default attendanceSlice.reducer;
