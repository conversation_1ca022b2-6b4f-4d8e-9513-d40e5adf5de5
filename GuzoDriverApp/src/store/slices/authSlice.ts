import {createSlice, createAsyncThunk, PayloadAction} from '@reduxjs/toolkit';
import {AuthState, LoginCredentials, User, ApiError} from '@types/index';
import {authAPI} from '@services/api/authAPI';
import {secureStorage} from '@services/secureStorage';

const initialState: AuthState = {
  user: null,
  token: null,
  isAuthenticated: false,
  isLoading: false,
  error: null,
};

// Async thunks
export const loginUser = createAsyncThunk<
  {user: User; token: string},
  LoginCredentials,
  {rejectValue: ApiError}
>('auth/login', async (credentials, {rejectWithValue}) => {
  try {
    const response = await authAPI.login(credentials);
    
    // Store token securely
    await secureStorage.setToken(response.data.token);
    
    return {
      user: response.data.user,
      token: response.data.token,
    };
  } catch (error: any) {
    return rejectWithValue({
      message: error.response?.data?.message || 'Login failed',
      status: error.response?.status,
    });
  }
});

export const logoutUser = createAsyncThunk<void, void, {rejectValue: ApiError}>(
  'auth/logout',
  async (_, {rejectWithValue}) => {
    try {
      await authAPI.logout();
      await secureStorage.removeToken();
    } catch (error: any) {
      // Even if logout API fails, we should clear local data
      await secureStorage.removeToken();
      return rejectWithValue({
        message: error.response?.data?.message || 'Logout failed',
        status: error.response?.status,
      });
    }
  },
);

export const checkAuthStatus = createAsyncThunk<
  {user: User; token: string} | null,
  void,
  {rejectValue: ApiError}
>('auth/checkStatus', async (_, {rejectWithValue}) => {
  try {
    const token = await secureStorage.getToken();
    if (!token) {
      return null;
    }

    const response = await authAPI.getProfile();
    return {
      user: response.data,
      token,
    };
  } catch (error: any) {
    await secureStorage.removeToken();
    return rejectWithValue({
      message: error.response?.data?.message || 'Authentication check failed',
      status: error.response?.status,
    });
  }
});

const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    clearError: state => {
      state.error = null;
    },
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },
  },
  extraReducers: builder => {
    // Login
    builder
      .addCase(loginUser.pending, state => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(loginUser.fulfilled, (state, action) => {
        state.isLoading = false;
        state.user = action.payload.user;
        state.token = action.payload.token;
        state.isAuthenticated = true;
        state.error = null;
      })
      .addCase(loginUser.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload?.message || 'Login failed';
        state.isAuthenticated = false;
      });

    // Logout
    builder
      .addCase(logoutUser.pending, state => {
        state.isLoading = true;
      })
      .addCase(logoutUser.fulfilled, state => {
        state.isLoading = false;
        state.user = null;
        state.token = null;
        state.isAuthenticated = false;
        state.error = null;
      })
      .addCase(logoutUser.rejected, (state, action) => {
        state.isLoading = false;
        // Still clear auth data even if logout API fails
        state.user = null;
        state.token = null;
        state.isAuthenticated = false;
        state.error = action.payload?.message || 'Logout failed';
      });

    // Check auth status
    builder
      .addCase(checkAuthStatus.pending, state => {
        state.isLoading = true;
      })
      .addCase(checkAuthStatus.fulfilled, (state, action) => {
        state.isLoading = false;
        if (action.payload) {
          state.user = action.payload.user;
          state.token = action.payload.token;
          state.isAuthenticated = true;
        } else {
          state.user = null;
          state.token = null;
          state.isAuthenticated = false;
        }
        state.error = null;
      })
      .addCase(checkAuthStatus.rejected, (state, action) => {
        state.isLoading = false;
        state.user = null;
        state.token = null;
        state.isAuthenticated = false;
        state.error = action.payload?.message || 'Authentication check failed';
      });
  },
});

export const {clearError, setLoading} = authSlice.actions;
export default authSlice.reducer;
