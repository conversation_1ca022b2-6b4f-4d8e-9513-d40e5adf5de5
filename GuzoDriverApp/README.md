# Guzo Sync Bus Driver Mobile Application

A React Native mobile application for bus drivers to manage their routes, track location, and mark attendance.

## Features

### Core Features (MVP)
- **Authentication**: Secure driver login with token-based authentication
- **Route Viewing**: Display assigned routes and schedules on an interactive map
- **GPS Tracking**: Real-time location tracking using device GPS
- **Attendance Marking**: Simple attendance marking with location verification
- **Dashboard**: Overview of daily schedule, attendance status, and location tracking

### Technical Features
- **Mapbox Integration**: High-quality maps with route visualization
- **Redux State Management**: Centralized state management with persistence
- **Secure Storage**: Encrypted storage for authentication tokens
- **Real-time Location**: Continuous GPS tracking with backend synchronization
- **Offline Support**: Basic offline functionality with data persistence

## Technology Stack

- **Framework**: React Native 0.73
- **State Management**: Redux Toolkit with Redux Persist
- **Navigation**: React Navigation 6
- **Maps**: Mapbox (@rnmapbox/maps)
- **UI Components**: React Native Paper
- **Forms**: React Hook Form with Yup validation
- **HTTP Client**: Axios
- **Secure Storage**: React Native Keychain
- **Location Services**: React Native Geolocation Service

## Prerequisites

- Node.js (>= 18)
- React Native CLI
- Android Studio (for Android development)
- Xcode (for iOS development)
- Mapbox Account and Access Token

## Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd GuzoDriverApp
   ```

2. **Install dependencies**
   ```bash
   pnpm install
   # or
   npm install
   ```

3. **Configure Mapbox**
   - Create a Mapbox account at https://mapbox.com
   - Get your access token from the Mapbox dashboard
   - Update the token in `src/config/constants/index.ts`:
     ```typescript
     export const MAPBOX_CONFIG = {
       ACCESS_TOKEN: 'YOUR_MAPBOX_ACCESS_TOKEN_HERE',
       // ...
     };
     ```
   - Also update the token in `src/screens/map/MapScreen.tsx`:
     ```typescript
     MapboxGL.setAccessToken('YOUR_MAPBOX_ACCESS_TOKEN_HERE');
     ```

4. **Configure Backend URL**
   - Update the API base URL in `src/config/constants/index.ts`:
     ```typescript
     export const API_CONFIG = {
       BASE_URL: 'https://your-backend-url.com',
       // ...
     };
     ```

5. **iOS Setup** (iOS only)
   ```bash
   cd ios && pod install && cd ..
   ```

## Running the Application

### Android
```bash
pnpm run android
# or
npm run android
```

### iOS
```bash
pnpm run ios
# or
npm run ios
```

### Start Metro Bundler
```bash
pnpm start
# or
npm start
```

## Project Structure

```
src/
├── api/                    # API service files
├── assets/                 # Images, fonts, icons
├── components/             # Reusable UI components
│   ├── common/            # Common components
│   ├── map/               # Map-specific components
│   └── ui/                # UI components (Button, Input, etc.)
├── config/                # Configuration files
│   └── constants/         # App constants
├── navigation/            # Navigation configuration
├── screens/               # Screen components
│   ├── auth/              # Authentication screens
│   ├── dashboard/         # Dashboard screen
│   ├── map/               # Map screen
│   ├── attendance/        # Attendance screen
│   └── profile/           # Profile screen
├── services/              # Business logic services
│   └── api/               # API service modules
├── store/                 # Redux store configuration
│   └── slices/            # Redux slices
├── theme/                 # Theme configuration
├── types/                 # TypeScript type definitions
└── utils/                 # Utility functions
```

## API Integration

The app integrates with the Guzo Sync Backend using the following endpoints:

### Authentication
- `POST /api/accounts/login` - Driver login
- `POST /api/accounts/logout` - Driver logout
- `GET /api/accounts/profile` - Get driver profile

### Routes & Schedules
- `GET /api/drivers/schedules` - Get driver schedules
- `GET /api/drivers/routes/{routeId}` - Get route details

### Attendance
- `POST /api/drivers/attendance` - Mark attendance
- `GET /api/drivers/attendance/today` - Get today's attendance

### Location Tracking
- `POST /internal/tracker/update-bus-location` - Update driver location

## Configuration

### Environment Variables
Create a `.env` file in the root directory:
```
API_BASE_URL=https://your-backend-url.com
MAPBOX_ACCESS_TOKEN=your_mapbox_token_here
```

### Permissions
The app requires the following permissions:
- **Location**: For GPS tracking and route navigation
- **Internet**: For API communication
- **Storage**: For offline data persistence

## Development

### Code Style
- ESLint and Prettier are configured for code formatting
- TypeScript is used for type safety
- Follow React Native best practices

### Testing
```bash
pnpm test
# or
npm test
```

### Building for Production

#### Android
```bash
pnpm run build:android
# or
npm run build:android
```

#### iOS
```bash
pnpm run build:ios
# or
npm run build:ios
```

## Troubleshooting

### Common Issues

1. **Metro bundler issues**
   ```bash
   pnpm start --reset-cache
   ```

2. **Android build issues**
   ```bash
   cd android && ./gradlew clean && cd ..
   ```

3. **iOS build issues**
   ```bash
   cd ios && pod install && cd ..
   ```

4. **Location permission issues**
   - Ensure location permissions are granted in device settings
   - Check that location services are enabled

5. **Mapbox issues**
   - Verify your Mapbox access token is valid
   - Check that the token has the necessary scopes

## Support

For technical support or questions:
- Email: <EMAIL>
- Phone: +251-911-123456

## License

© 2024 Guzo Sync Transport. All rights reserved.
