{"name": "GuzoDriverApp", "version": "1.0.0", "description": "Guzo Sync Bus Driver Mobile Application", "main": "index.js", "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "start": "react-native start", "test": "jest", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "build:android": "cd android && ./gradlew assembleRelease", "build:ios": "cd ios && xcodebuild -workspace GuzoDriverApp.xcworkspace -scheme GuzoDriverApp -configuration Release"}, "dependencies": {"@hookform/resolvers": "^3.3.2", "@react-native-async-storage/async-storage": "^2.1.2", "@react-navigation/bottom-tabs": "^6.5.11", "@react-navigation/native": "^6.1.9", "@react-navigation/stack": "^6.3.20", "@reduxjs/toolkit": "^2.0.1", "@rnmapbox/maps": "^10.1.11", "axios": "^1.6.2", "expo": "^53.0.9", "react": "19.0.0", "react-hook-form": "^7.48.2", "react-native": "0.79.2", "react-native-geolocation-service": "^5.3.1", "react-native-gesture-handler": "^2.24.0", "react-native-keychain": "^8.1.3", "react-native-paper": "^5.11.6", "react-native-permissions": "^4.1.0", "react-native-reanimated": "^3.17.5", "react-native-safe-area-context": "^5.4.0", "react-native-screens": "^4.10.0", "react-native-toast-message": "^2.2.0", "react-native-vector-icons": "^10.0.3", "react-redux": "^9.0.4", "redux-persist": "^6.0.0", "yup": "^1.4.0"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/preset-env": "^7.20.0", "@babel/runtime": "^7.20.0", "@react-native/babel-preset": "^0.73.18", "@react-native/eslint-config": "^0.73.1", "@react-native/metro-config": "^0.73.2", "@react-native/typescript-config": "^0.73.1", "@types/react": "^19.0.14", "@types/react-test-renderer": "^18.0.0", "babel-jest": "^29.6.3", "eslint": "^8.19.0", "jest": "^29.6.3", "metro-react-native-babel-preset": "0.77.0", "prettier": "2.8.8", "react-test-renderer": "18.2.0", "typescript": "5.8.3"}, "engines": {"node": ">=18"}, "packageManager": "pnpm@10.6.3"}